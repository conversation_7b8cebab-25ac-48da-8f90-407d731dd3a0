"use client"

import { useEffect, useRef, useState } from "react"
import Image from "next/image"
import Link from "next/link"
import { motion, AnimatePresence } from "framer-motion"
import { But<PERSON> } from "@/components/ui/button"
import projectsData from "@/data/projects.json"
import { generateSlug } from "@/lib/utils"

export default function ProjectsSection() {
  const sectionRef = useRef<HTMLElement>(null)
  const [projects] = useState(projectsData.slice(0, 3))
  const [currentImages, setCurrentImages] = useState<number[]>([0, 0, 0])

  useEffect(() => {
    const interval = setInterval(() => {
      setCurrentImages(prev =>
        prev.map((current, index) => {
          const project = projects[index]
          if (project && project.imageUrls && project.imageUrls.length > 1) {
            return (current + 1) % project.imageUrls.length
          }
          return current
        })
      )
    }, 4000)

    return () => clearInterval(interval)
  }, [projects])

  const handleClick = (index: number) => {
    setCurrentImages(prev => {
      const newImages = [...prev]
      const project = projects[index]
      if (project && project.imageUrls && project.imageUrls.length > 1) {
        newImages[index] = (newImages[index] + 1) % project.imageUrls.length
      }
      return newImages
    })
  }

  return (
    <section ref={sectionRef} className="py-20 bg-white">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <motion.div
          className="text-center mb-16"
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
          viewport={{ once: true }}
        >
          <h2 className="text-4xl font-bold text-gray-900 mb-4 font-sinkin header-stylish">
            <span className="text-primary-part">Our</span> <span className="text-brand-part">Projects</span>
          </h2>
          <p className="text-xl text-gray-600 font-exo">
            Showcasing our latest architectural achievements
          </p>
        </motion.div>
        <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8 mb-12">
          {projects.map((project, index) => (
            <motion.div
              key={project.id}
              className="flex-1 bg-white text-black rounded-lg overflow-hidden relative cursor-pointer shadow-lg hover:shadow-xl"
              onClick={() => handleClick(index)}
              initial={{ opacity: 0 }}
              whileInView={{ opacity: 1 }}
              transition={{ duration: 0.5, delay: index * 0.1 }}
              viewport={{ once: true }}
              whileHover={{ y: -5 }}
            >
              <div className="relative w-full pb-[75%] transition-transform duration-700 transform perspective-1000">
                <AnimatePresence mode="wait">
                  <motion.div
                    key={currentImages[index]}
                    className="absolute inset-0 backface-hidden"
                    initial={{ opacity: 0, rotateY: 90 }}
                    whileInView={{ opacity: 1, rotateY: 0 }}
                    exit={{ opacity: 0, rotateY: -90 }}
                    transition={{ duration: 0.7, delay: 0.2 }}
                  >
                    <Image
                      src={project.imageUrls?.[currentImages[index]] || project.image || "/placeholder.svg"}
                      alt={project.title}
                      fill
                      className="object-cover"
                    />
                  </motion.div>
                </AnimatePresence>
                <div className="absolute inset-0 bg-black/40 opacity-0 hover:opacity-100 transition-opacity duration-300 flex items-center justify-center">
                  <Link href={`/work/${generateSlug(project.title)}`}>
                    <Button className="bg-white text-gray-900 hover:bg-gray-100">View Project</Button>
                  </Link>
                </div>
              </div>
              <motion.div
                className="p-4"
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5, delay: 0.3 }}
              >
                <h3 className="text-xl font-semibold font-sinkin">{project.title}</h3>
                <p className="flex items-center gap-2 mt-2 mb-4 text-gray-600 font-exo">
                  {project.location}
                </p>
              </motion.div>
            </motion.div>
          ))}
        </div>
        <motion.div
          className="text-center"
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.4 }}
          viewport={{ once: true }}
        >
          <Link href="/work">
            <Button size="lg" className="bg-brand-primary hover:bg-brand-accent text-white font-exo">
              View More Projects
            </Button>
          </Link>
        </motion.div>
      </div>
    </section>
  )
}
