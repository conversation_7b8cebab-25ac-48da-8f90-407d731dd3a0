"use client"

import { useEffect, useRef, useState } from "react"
import Image from "next/image"
import Link from "next/link"
import { But<PERSON> } from "@/components/ui/button"
import projectsData from "@/data/projects.json"
import { generateSlug } from "@/lib/utils"

export default function ProjectsSection() {
  const sectionRef = useRef<HTMLElement>(null)
  const [projects] = useState(projectsData.slice(0, 3))

  useEffect(() => {
    const observer = new IntersectionObserver(
      (entries) => {
        entries.forEach((entry) => {
          if (entry.isIntersecting) {
            entry.target.classList.add("animate-fade-in-up")
          }
        })
      },
      { threshold: 0.1 },
    )

    const elements = sectionRef.current?.querySelectorAll(".animate-on-scroll")
    elements?.forEach((el) => observer.observe(el))

    return () => observer.disconnect()
  }, [])

  return (
    <section ref={sectionRef} className="py-20 bg-white">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center mb-16 animate-on-scroll">
          <h2 className="text-4xl font-bold text-gray-900 mb-4 animate-on-scroll">Our Projects</h2>
          <p className="text-xl text-gray-600 animate-on-scroll animation-delay-100">
            Showcasing our latest architectural achievements
          </p>
        </div>
        <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8 mb-12">
          {projects.map((project, index) => (
            <div key={project.id} className={`group animate-on-scroll animation-delay-${(index + 1) * 100}`}>
              <div className="relative overflow-hidden rounded-lg shadow-lg animate-on-scroll animation-delay-${(index + 1) * 150}">
                <Image
                  src={project.image || "/placeholder.svg"}
                  alt={project.title}
                  width={400}
                  height={300}
                  className="w-full h-80 object-cover group-hover:scale-105 transition-transform duration-300"
                />
                <div className="absolute inset-0 bg-black/40 opacity-0 group-hover:opacity-100 transition-opacity duration-300 flex items-center justify-center">
                  <Link href={`/work/${generateSlug(project.title)}`}>
                    <Button className="bg-white text-gray-900 hover:bg-gray-100">View Project</Button>
                  </Link>
                </div>
              </div>
              <div className="mt-4">
                <h3 className="text-xl font-semibold text-gray-900 mb-2 animate-on-scroll animation-delay-${(index + 1) * 200}">
                  {project.title}
                </h3>
                <p className="text-gray-600 mb-2 animate-on-scroll animation-delay-${(index + 1) * 250}">
                  {project.category}
                </p>
                <p className="text-sm text-gray-500 animate-on-scroll animation-delay-${(index + 1) * 300}">
                  {project.location}
                </p>
              </div>
            </div>
          ))}
        </div>
        <div className="text-center animate-on-scroll animation-delay-400">
          <Link href="/work">
            <Button size="lg" className="bg-brand-primary hover:bg-brand-accent text-white">
              View More Projects
            </Button>
          </Link>
        </div>
      </div>
    </section>
  )
}
