"use client"

import { useEffect, useRef } from "react"
import Image from "next/image"

export default function UnveilingSection() {
  const sectionRef = useRef<HTMLElement>(null)

  useEffect(() => {
    const observer = new IntersectionObserver(
      (entries) => {
        entries.forEach((entry) => {
          if (entry.isIntersecting) {
            entry.target.classList.add("animate-fade-in-up")
          }
        })
      },
      { threshold: 0.1 },
    )

    const elements = sectionRef.current?.querySelectorAll(".animate-on-scroll")
    elements?.forEach((el) => observer.observe(el))

    return () => observer.disconnect()
  }, [])

  return (
    <section ref={sectionRef} className="py-20 bg-gray-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="grid lg:grid-cols-2 gap-12 items-center">
          <div className="animate-on-scroll">
            <Image
              src="/img/photo/office4.webp"
              alt="Unveiling the Potential"
              width={600}
              height={400}
              className="rounded-lg shadow-lg h-[550px] object-cover"
            />
          </div>
          <div className="animate-on-scroll animation-delay-200">
            <h2 className="text-4xl font-bold text-gray-900 mb-6">Unveiling the Potential in Every Situation</h2>
            <div className="space-y-4 text-lg text-gray-600 leading-relaxed">
              <p>
                Just as CARVE means to artfully craft out beautiful desired outcomes out of basic and crude items, we at
                CARVE ATELIER are driven by the vision of gracefully carving out creative architectural design &
                solutions out of fallow situations or complex challenges.
              </p>
              <p>
                With the quality of craft that carving entails, we sculpt every detail with style and precision powered
                by rigorous research, innovative design processes and a clear focus on the goals of our clients.
              </p>
              <p className="font-semibold text-brand-primary">
                We love to bring out the best out of everything
                <br />
                We love Carving
                <br />
                We are CARVE ATELIER
              </p>
              <p className="text-sm text-gray-500 italic">
                atelier : [Translation] - a workshop or studio, especially one used by an artist or designer
              </p>
            </div>
          </div>
        </div>
      </div>
    </section>
  )
}
