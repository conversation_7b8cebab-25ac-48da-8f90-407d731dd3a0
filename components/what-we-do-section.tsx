"use client"

import { useEffect, useRef } from "react"

const services = [
  {
    title: "Commercial",
    description:
      "We excel in creating dynamic and functional commercial spaces tailored to our clients' business needs. Our expertise in commercial design includes innovative solutions that drive business success.",
  },
  {
    title: "Retail Design",
    description:
      "Transforming retail environments into captivating and customer-centric spaces is our specialty. We create immersive shopping experiences that connect brands with their customers.",
  },
  {
    title: "Residential",
    description:
      "We design homes that resonate with the lifestyles and aspirations of our clients. Each residential project reflects the unique personality and needs of those who will call it home.",
  },
  {
    title: "Master Planning & Urban Design",
    description:
      "We specialize in shaping communities through thoughtful master planning and urban design strategies that create sustainable, livable environments for future generations.",
  },
]

export default function WhatWeDoSection() {
  const sectionRef = useRef<HTMLElement>(null)

  useEffect(() => {
    const observer = new IntersectionObserver(
      (entries) => {
        entries.forEach((entry) => {
          if (entry.isIntersecting) {
            entry.target.classList.add("animate-fade-in-up")
          }
        })
      },
      { threshold: 0.1 },
    )

    const elements = sectionRef.current?.querySelectorAll(".animate-on-scroll")
    elements?.forEach((el) => observer.observe(el))

    return () => observer.disconnect()
  }, [])

  return (
    <section ref={sectionRef} className="py-20 bg-white">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center mb-16 animate-on-scroll">
          <h2 className="text-4xl font-bold text-gray-900 mb-6">What We Do</h2>
          <p className="text-lg text-gray-600 max-w-4xl mx-auto leading-relaxed">
            CARVE Atelier is an Architecture and Design firm specialized in crafting Innovative and practical solutions
            to projects, with a primary focus on fulfilling our client goals. Fueled by an deep-seated passion for
            design excellence, our approach is driven by principles aimed at transforming existing sites in a unique
            ways that address programmatic and operational needs. We provide professional services that not only meet
            but exceed our clients' expectations, delivering substantial value.
          </p>
        </div>
        <div className="grid md:grid-cols-2 gap-8">
          {services.map((service, index) => (
            <div
              key={service.title}
              className={`bg-gradient-to-br from-brand-light to-white p-8 rounded-lg shadow-lg hover:shadow-xl transition-all duration-300 animate-on-scroll animation-delay-${(index + 1) * 100}`}
            >
              <h3 className="text-2xl font-semibold text-gray-900 mb-4">{service.title}</h3>
              <p className="text-gray-600 leading-relaxed">{service.description}</p>
            </div>
          ))}
        </div>
      </div>
    </section>
  )
}
