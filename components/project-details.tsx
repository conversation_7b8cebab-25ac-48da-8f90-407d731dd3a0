"use client"

import { useState, useEffect, useRef } from "react"
import Image from "next/image"
import Link from "next/link"
import { ArrowLeft, MapPin, Calendar, User, Building } from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import projectsData from "@/data/projects.json"
import { findProjectBySlug } from "@/lib/utils"

interface ProjectDetailsProps {
  projectSlug: string
}

export default function ProjectDetails({ projectSlug }: ProjectDetailsProps) {
  const [project, setProject] = useState<any>(null)
  const sectionRef = useRef<HTMLElement>(null)

  useEffect(() => {
    const foundProject = findProjectBySlug(projectsData, projectSlug)
    setProject(foundProject)
  }, [projectSlug])

  useEffect(() => {
    const observer = new IntersectionObserver(
      (entries) => {
        entries.forEach((entry) => {
          if (entry.isIntersecting) {
            entry.target.classList.add("animate-fade-in-up")
          }
        })
      },
      { threshold: 0.1 },
    )

    const elements = sectionRef.current?.querySelectorAll(".animate-on-scroll")
    elements?.forEach((el) => observer.observe(el))

    return () => observer.disconnect()
  }, [project])

  if (!project) {
    return <div className="py-20 text-center">Project not found</div>
  }

  return (
    <section ref={sectionRef} className="py-20 bg-white">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="mb-8 animate-on-scroll">
          <Link href="/work">
            <Button variant="outline" className="mb-6">
              <ArrowLeft className="mr-2 h-4 w-4" />
              Back to Projects
            </Button>
          </Link>
        </div>

        {/* Project Info */}
        <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-6 mb-12 animate-on-scroll">
          <div className="flex items-center space-x-3">
            <Building className="h-5 w-5 text-brand-primary" />
            <div>
              <p className="text-sm text-gray-500">Project Type</p>
              <p className="font-semibold">{project.type}</p>
            </div>
          </div>
          <div className="flex items-center space-x-3">
            <MapPin className="h-5 w-5 text-brand-primary" />
            <div>
              <p className="text-sm text-gray-500">Location</p>
              <p className="font-semibold">{project.location}</p>
            </div>
          </div>
          <div className="flex items-center space-x-3">
            <Calendar className="h-5 w-5 text-brand-primary" />
            <div>
              <p className="text-sm text-gray-500">Status</p>
              <p className="font-semibold">{project.status}</p>
            </div>
          </div>
          <div className="flex items-center space-x-3">
            <User className="h-5 w-5 text-brand-primary" />
            <div>
              <p className="text-sm text-gray-500">Client</p>
              <p className="font-semibold">{project.client}</p>
            </div>
          </div>
        </div>

        {/* Main Image */}
        <div className="mb-12 animate-on-scroll">
          <Image
            src={project.image || "/placeholder.svg"}
            alt={project.title}
            width={1200}
            height={600}
            className="w-full h-[500px] object-cover rounded-lg shadow-lg"
          />
        </div>

        {/* Description */}
        <div className="grid lg:grid-cols-3 gap-12 mb-12">
          <div className="lg:col-span-2 animate-on-scroll">
            <h2 className="text-2xl font-bold text-gray-900 mb-6">Project Overview</h2>
            <p className="text-lg text-gray-600 mb-6 leading-relaxed">{project.description}</p>
            <p className="text-lg text-gray-600 leading-relaxed">{project.fullDescription}</p>
          </div>
          <div className="animate-on-scroll animation-delay-200">
            <h3 className="text-xl font-bold text-gray-900 mb-4">Project Highlights</h3>
            <ul className="space-y-2 text-gray-600">
              <li>• Sustainable design principles</li>
              <li>• Energy-efficient systems</li>
              <li>• Modern architectural aesthetics</li>
              <li>• Functional space optimization</li>
            </ul>
          </div>
        </div>

        {/* Features */}
        {project.features && (
          <div className="mb-12 animate-on-scroll">
            <h2 className="text-2xl font-bold text-gray-900 mb-8">Key Features</h2>
            <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
              {project.features.map((feature: any, index: number) => (
                <div key={feature.title} className={`animate-on-scroll animation-delay-${(index + 1) * 100}`}>
                  <h3 className="text-xl font-semibold text-gray-900 mb-3">{feature.title}</h3>
                  <p className="text-gray-600 leading-relaxed">{feature.description}</p>
                </div>
              ))}
            </div>
          </div>
        )}

        {/* Project Images */}
        {project.images && project.images.length > 1 && (
          <div className="animate-on-scroll">
            <h2 className="text-2xl font-bold text-gray-900 mb-8">Project Gallery</h2>
            <div className="grid md:grid-cols-2 gap-8">
              {project.images.slice(1).map((image: string, index: number) => (
                <div key={index} className={`animate-on-scroll animation-delay-${(index + 1) * 100}`}>
                  <Image
                    src={image || "/placeholder.svg"}
                    alt={`${project.title} - Image ${index + 2}`}
                    width={600}
                    height={400}
                    className="w-full h-80 object-cover rounded-lg shadow-lg"
                  />
                </div>
              ))}
            </div>
          </div>
        )}
      </div>
    </section>
  )
}
