"use client"

import { useEffect, useRef } from "react"
import Image from "next/image"

interface PageBannerProps {
  title: string
  subtitle?: string
  backgroundImage?: string
  breadcrumbs?: Array<{ label: string; href?: string }>
}

export default function PageBanner({ title, subtitle, backgroundImage, breadcrumbs }: PageBannerProps) {
  const bannerRef = useRef<HTMLElement>(null)

  useEffect(() => {
    const observer = new IntersectionObserver(
      (entries) => {
        entries.forEach((entry) => {
          if (entry.isIntersecting) {
            entry.target.classList.add("animate-fade-in-up")
          }
        })
      },
      { threshold: 0.1 },
    )

    const elements = bannerRef.current?.querySelectorAll(".animate-on-scroll")
    elements?.forEach((el) => observer.observe(el))

    return () => observer.disconnect()
  }, [])

  return (
    <section ref={bannerRef} className="relative h-[60vh] min-h-[400px] overflow-hidden">
      {/* Background Image */}
      <div className="absolute inset-0 animate-on-scroll">
        <Image
          src={backgroundImage || "/placeholder.svg?height=600&width=1200"}
          alt={title}
          fill
          className="object-cover"
          priority
        />
        <div className="absolute inset-0 bg-gradient-to-r from-black/60 via-black/40 to-black/60" />
      </div>

      {/* Content */}
      <div className="relative h-full flex items-center justify-center">
        <div className="text-center text-white max-w-4xl px-4">
          {breadcrumbs && (
            <nav className="mb-4 animate-on-scroll animation-delay-100">
              <ol className="flex justify-center space-x-2 text-sm">
                {breadcrumbs.map((crumb, index) => (
                  <li key={index} className="flex items-center">
                    {index > 0 && <span className="mx-2 text-brand-light">/</span>}
                    {crumb.href ? (
                      <a href={crumb.href} className="hover:text-brand-secondary transition-colors">
                        {crumb.label}
                      </a>
                    ) : (
                      <span className="text-brand-light">{crumb.label}</span>
                    )}
                  </li>
                ))}
              </ol>
            </nav>
          )}

          <h1 className="text-4xl md:text-5xl lg:text-6xl font-bold mb-6 animate-on-scroll animation-delay-200">
            {title}
          </h1>

          {subtitle && (
            <p className="text-xl md:text-2xl text-gray-200 animate-on-scroll animation-delay-400">{subtitle}</p>
          )}
        </div>
      </div>

      {/* Decorative Elements */}
      <div className="absolute bottom-0 left-0 right-0 h-32 bg-gradient-to-t from-white via-white/50 to-transparent animate-on-scroll animation-delay-600" />
    </section>
  )
}
