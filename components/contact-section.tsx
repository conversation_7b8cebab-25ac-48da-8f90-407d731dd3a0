"use client"

import type React from "react"

import { useState, useEffect, useRef } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Textarea } from "@/components/ui/textarea"
import { Mail, Phone, MapPin } from "lucide-react"

const animatedTitles = [
  "Do you have any questions?",
  "Ready to start your project?",
  "Need architectural consultation?",
  "Let's create something amazing together!",
]

export default function ContactSection() {
  const [currentTitleIndex, setCurrentTitleIndex] = useState(0)
  const [formData, setFormData] = useState({
    name: "",
    email: "",
    phone: "",
    message: "",
  })
  const sectionRef = useRef<HTMLElement>(null)

  useEffect(() => {
    const timer = setInterval(() => {
      setCurrentTitleIndex((prev) => (prev + 1) % animatedTitles.length)
    }, 3000)
    return () => clearInterval(timer)
  }, [])

  useEffect(() => {
    const observer = new IntersectionObserver(
      (entries) => {
        entries.forEach((entry) => {
          if (entry.isIntersecting) {
            entry.target.classList.add("animate-fade-in-up")
          }
        })
      },
      { threshold: 0.1 },
    )

    const elements = sectionRef.current?.querySelectorAll(".animate-on-scroll")
    elements?.forEach((el) => observer.observe(el))

    return () => observer.disconnect()
  }, [])

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault()
    console.log("Form submitted:", formData)
    // Handle form submission here
  }

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    setFormData((prev) => ({
      ...prev,
      [e.target.name]: e.target.value,
    }))
  }

  return (
    <section ref={sectionRef} className="py-20 bg-white">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center mb-16 animate-on-scroll">
          <h2 className="text-4xl font-bold text-gray-900 mb-4 h-16 flex items-center justify-center">
            <span className="animate-fade-in-out" key={currentTitleIndex}>
              {animatedTitles[currentTitleIndex]}
            </span>
          </h2>
        </div>
        <div className="grid lg:grid-cols-2 gap-12">
          <div className="animate-on-scroll">
            <form onSubmit={handleSubmit} className="space-y-6">
              <div className="grid md:grid-cols-2 gap-6">
                <div>
                  <label htmlFor="name" className="block text-sm font-medium text-gray-700 mb-2">
                    Full Name
                  </label>
                  <Input
                    id="name"
                    name="name"
                    type="text"
                    required
                    value={formData.name}
                    onChange={handleChange}
                    className="w-full"
                  />
                </div>
                <div>
                  <label htmlFor="email" className="block text-sm font-medium text-gray-700 mb-2">
                    Email Address
                  </label>
                  <Input
                    id="email"
                    name="email"
                    type="email"
                    required
                    value={formData.email}
                    onChange={handleChange}
                    className="w-full"
                  />
                </div>
              </div>
              <div>
                <label htmlFor="phone" className="block text-sm font-medium text-gray-700 mb-2">
                  Phone Number
                </label>
                <Input
                  id="phone"
                  name="phone"
                  type="tel"
                  value={formData.phone}
                  onChange={handleChange}
                  className="w-full"
                />
              </div>
              <div>
                <label htmlFor="message" className="block text-sm font-medium text-gray-700 mb-2">
                  Message
                </label>
                <Textarea
                  id="message"
                  name="message"
                  rows={6}
                  required
                  value={formData.message}
                  onChange={handleChange}
                  className="w-full"
                />
              </div>
              <Button type="submit" size="lg" className="w-full bg-brand-primary hover:bg-brand-accent text-white">
                Send Message
              </Button>
            </form>
          </div>
          <div className="animate-on-scroll animation-delay-200">
            <div className="space-y-8">
              <div className="flex items-start space-x-4">
                <div className="bg-brand-light p-3 rounded-full">
                  <MapPin className="h-6 w-6 text-brand-primary" />
                </div>
                <div>
                  <h3 className="text-lg font-semibold text-gray-900 mb-2">Address</h3>
                  <p className="text-gray-600">
                    No 57, Isaac John Street
                    <br />
                    Yaba, Lagos
                    <br />
                    Nigeria
                  </p>
                </div>
              </div>
              <div className="flex items-start space-x-4">
                <div className="bg-brand-light p-3 rounded-full">
                  <Phone className="h-6 w-6 text-brand-primary" />
                </div>
                <div>
                  <h3 className="text-lg font-semibold text-gray-900 mb-2">Phone</h3>
                  <p className="text-gray-600">
                    +234 9064525110
                    <br />
                    01-7916445
                  </p>
                </div>
              </div>
              <div className="flex items-start space-x-4">
                <div className="bg-brand-light p-3 rounded-full">
                  <Mail className="h-6 w-6 text-brand-primary" />
                </div>
                <div>
                  <h3 className="text-lg font-semibold text-gray-900 mb-2">Email</h3>
                  <p className="text-gray-600"><EMAIL></p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  )
}
