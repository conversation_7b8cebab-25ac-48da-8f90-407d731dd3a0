"use client"

import { useState, useEffect, useRef } from "react"
import Image from "next/image"
import Link from "next/link"
import { But<PERSON> } from "@/components/ui/button"
import projectsData from "@/data/projects.json"
import { generateSlug } from "@/lib/utils"

const categories = ["All", "Commercial", "Retail", "Residential", "Urban Design & Masterplan"]

export default function WorkGrid() {
  const [filteredProjects, setFilteredProjects] = useState(projectsData)
  const [activeCategory, setActiveCategory] = useState("All")
  const [visibleCount, setVisibleCount] = useState(6)
  const sectionRef = useRef<HTMLElement>(null)

  useEffect(() => {
    const observer = new IntersectionObserver(
      (entries) => {
        entries.forEach((entry) => {
          if (entry.isIntersecting) {
            entry.target.classList.add("animate-fade-in-up")
          }
        })
      },
      { threshold: 0.1 },
    )

    const elements = sectionRef.current?.querySelectorAll(".animate-on-scroll")
    elements?.forEach((el) => observer.observe(el))

    return () => observer.disconnect()
  }, [filteredProjects, visibleCount])

  const filterProjects = (category: string) => {
    setActiveCategory(category)
    if (category === "All") {
      setFilteredProjects(projectsData)
    } else {
      setFilteredProjects(projectsData.filter((project) => project.category === category))
    }
    setVisibleCount(6)
  }

  const loadMore = () => {
    setVisibleCount((prev) => prev + 6)
  }

  const visibleProjects = filteredProjects.slice(0, visibleCount)

  return (
    <section ref={sectionRef} className="py-20 bg-white">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Filter Buttons */}
        <div className="flex flex-wrap justify-center gap-4 mb-12 animate-on-scroll">
          {categories.map((category) => (
            <Button
              key={category}
              variant={activeCategory === category ? "default" : "outline"}
              onClick={() => filterProjects(category)}
              className={
                activeCategory === category
                  ? "bg-brand-primary hover:bg-brand-accent text-white"
                  : "hover:text-brand-primary hover:border-brand-primary"
              }
            >
              {category}
            </Button>
          ))}
        </div>

        {/* Projects Grid */}
        <div className="grid md:grid-cols-2 gap-8 mb-12">
          {visibleProjects.map((project, index) => (
            <div key={project.id} className="group animate-on-scroll">
              <div className="relative overflow-hidden rounded-lg shadow-lg">
                <Image
                  src={project.image || "/placeholder.svg"}
                  alt={project.title}
                  width={600}
                  height={400}
                  className="w-full h-96 object-cover group-hover:scale-105 transition-transform duration-300"
                />
                <div className="absolute inset-0 bg-black/40 opacity-0 group-hover:opacity-100 transition-opacity duration-300 flex items-center justify-center">
                  <Link href={`/work/${generateSlug(project.title)}`}>
                    <Button className="bg-white text-gray-900 hover:bg-gray-100">View Project</Button>
                  </Link>
                </div>
              </div>
              <div className="mt-6">
                <h3 className="text-2xl font-semibold text-gray-900 mb-2">{project.title}</h3>
                <p className="text-brand-primary font-medium text-lg">{project.category}</p>
              </div>
            </div>
          ))}
        </div>

        {/* Load More Button */}
        {visibleCount < filteredProjects.length && (
          <div className="text-center animate-on-scroll">
            <Button onClick={loadMore} size="lg" className="bg-brand-primary hover:bg-brand-accent text-white">
              Load More Projects
            </Button>
          </div>
        )}
      </div>
    </section>
  )
}
