"use client"

import { useState, useEffect, useRef } from "react"
import { <PERSON>uo<PERSON>, ChevronLeft, ChevronRight } from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"

const testimonials = [
  {
    name: "<PERSON> Virtue",
    role: "Property Developer",
    company: "Virtue Holdings",
    content:
      "Working with CARVE Atelier was an exceptional experience. Their team understood our vision perfectly and delivered a commercial space that exceeded our expectations. The attention to detail and innovative design approach made all the difference.",
    rating: 5,
  },
  {
    name: "<PERSON>",
    role: "Retail Manager",
    company: "Miller & Associates",
    content:
      "CARVE Atelier transformed our retail space into something truly spectacular. Their understanding of customer flow and brand identity resulted in a 40% increase in foot traffic. Highly recommended for any retail project.",
    rating: 5,
  },
  {
    name: "<PERSON>",
    role: "CEO",
    company: "Trueman Enterprises",
    content:
      "The residential project CARVE Atelier designed for us perfectly balances luxury with functionality. Every space feels thoughtfully planned, and the sustainable design elements align perfectly with our values.",
    rating: 5,
  },
  {
    name: "<PERSON>",
    role: "Urban Planner",
    company: "City Development Corp",
    content:
      "Their master planning expertise is unmatched. CARVE Atelier's approach to community-centered design and sustainable development has set a new standard for urban projects in our region.",
    rating: 5,
  },
]

export default function TestimonialsSection() {
  const [currentTestimonial, setCurrentTestimonial] = useState(0)
  const sectionRef = useRef<HTMLElement>(null)

  useEffect(() => {
    const observer = new IntersectionObserver(
      (entries) => {
        entries.forEach((entry) => {
          if (entry.isIntersecting) {
            entry.target.classList.add("animate-fade-in-up")
          }
        })
      },
      { threshold: 0.1 },
    )

    const elements = sectionRef.current?.querySelectorAll(".animate-on-scroll")
    elements?.forEach((el) => observer.observe(el))

    return () => observer.disconnect()
  }, [])

  useEffect(() => {
    const timer = setInterval(() => {
      setCurrentTestimonial((prev) => (prev + 1) % testimonials.length)
    }, 8000)
    return () => clearInterval(timer)
  }, [])

  const nextTestimonial = () => {
    setCurrentTestimonial((prev) => (prev + 1) % testimonials.length)
  }

  const prevTestimonial = () => {
    setCurrentTestimonial((prev) => (prev - 1 + testimonials.length) % testimonials.length)
  }

  const goToTestimonial = (index: number) => {
    setCurrentTestimonial(index)
  }

  return (
    <section ref={sectionRef} className="py-20 bg-gray-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center mb-16 animate-on-scroll">
          <h2 className="text-4xl font-bold mb-4 header-stylish font-sinkin">
            <span className="text-primary-part">What Our Clients</span> <span className="text-brand-part">Say</span>
          </h2>
          <p className="text-xl text-gray-600 font-exo">Trusted by leading organizations and individuals</p>
        </div>

        <div className="relative max-w-4xl mx-auto">
          {/* Main Testimonial Display */}
          <div className="bg-white rounded-2xl shadow-xl p-8 md:p-12 animate-on-scroll">
            <div className="text-center">
              <Quote className="h-12 w-12 text-brand-primary mx-auto mb-6" />

              <div className="mb-8">
                <p className="text-xl md:text-2xl text-gray-700 leading-relaxed italic mb-6">
                  "{testimonials[currentTestimonial].content}"
                </p>

                {/* Star Rating */}
                <div className="flex justify-center mb-6">
                  {[...Array(testimonials[currentTestimonial].rating)].map((_, i) => (
                    <svg key={i} className="w-5 h-5 text-yellow-400 fill-current" viewBox="0 0 20 20">
                      <path d="M10 15l-5.878 3.09 1.123-6.545L.489 6.91l6.572-.955L10 0l2.939 5.955 6.572.955-4.756 4.635 1.123 6.545z" />
                    </svg>
                  ))}
                </div>
              </div>

              {/* Client Info */}
              <div className="border-t pt-6">
                <div className="flex items-center justify-center">
                  <div className="w-16 h-16 bg-brand-light rounded-full flex items-center justify-center mr-4">
                    <span className="text-brand-primary font-bold text-xl">
                      {testimonials[currentTestimonial].name
                        .split(" ")
                        .map((n) => n[0])
                        .join("")}
                    </span>
                  </div>
                  <div className="text-left">
                    <h4 className="text-xl font-bold text-gray-900">{testimonials[currentTestimonial].name}</h4>
                    <p className="text-brand-primary font-medium">{testimonials[currentTestimonial].role}</p>
                    <p className="text-gray-500 text-sm">{testimonials[currentTestimonial].company}</p>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Navigation Arrows */}
          <Button
            variant="ghost"
            size="icon"
            className="absolute left-4 top-1/2 transform -translate-y-1/2 bg-white shadow-lg hover:bg-gray-50 text-gray-600"
            onClick={prevTestimonial}
          >
            <ChevronLeft className="h-6 w-6" />
          </Button>
          <Button
            variant="ghost"
            size="icon"
            className="absolute right-4 top-1/2 transform -translate-y-1/2 bg-white shadow-lg hover:bg-gray-50 text-gray-600"
            onClick={nextTestimonial}
          >
            <ChevronRight className="h-6 w-6" />
          </Button>

          {/* Dots Indicator */}
          <div className="flex justify-center mt-8 space-x-2">
            {testimonials.map((_, index) => (
              <button
                key={index}
                className={`w-3 h-3 rounded-full transition-all duration-300 ${
                  index === currentTestimonial ? "bg-brand-primary scale-125" : "bg-gray-300 hover:bg-gray-400"
                }`}
                onClick={() => goToTestimonial(index)}
              />
            ))}
          </div>
        </div>

      </div>
    </section>
  )
}
