"use client"

import { useState, useEffect } from "react"
import Navbar from "@/components/navbar"
import HeroSection from "@/components/hero-section"
import AboutSection from "@/components/about-section"
import ServicesSection from "@/components/services-section"

import VideoBreakSection from "@/components/video-break-section"
import ProjectsSection from "@/components/projects-section"
import NewsSection from "@/components/news-section"
import ContactSection from "@/components/contact-section"
import Footer from "@/components/footer"
import LoadingScreen from "@/components/loading-screen"

export default function HomePage() {
  const [isLoading, setIsLoading] = useState(true)
  const [showContent, setShowContent] = useState(false)

  useEffect(() => {
    // Check if user has visited before (skip loading for returning users)
    const hasVisited = sessionStorage.getItem('hasVisited')

    if (hasVisited) {
      setIsLoading(false)
      setShowContent(true)
    } else {
      // Mark as visited for this session
      sessionStorage.setItem('hasVisited', 'true')
    }
  }, [])

  // Ensure scroll events work after loading
  useEffect(() => {
    if (!isLoading && showContent) {
      // Force a scroll event to initialize navbar state
      window.dispatchEvent(new Event('scroll'))
    }
  }, [isLoading, showContent])

  const handleLoadingComplete = () => {
    setIsLoading(false)
    // Small delay to ensure smooth transition
    setTimeout(() => {
      setShowContent(true)
    }, 100)
  }

  return (
    <>
      {isLoading && <LoadingScreen onComplete={handleLoadingComplete} />}

      {/* Navbar outside main content for proper fixed positioning */}
      <Navbar />

      <main
        className={`min-h-screen transition-all duration-1000 ${
          showContent ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-4'
        }`}
      >
        <HeroSection />
        <AboutSection />
        <ServicesSection />
        <VideoBreakSection />
        <ProjectsSection />
        <NewsSection />
        <ContactSection />
        <Footer />
      </main>
    </>
  )
}
