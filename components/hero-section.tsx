"use client"

import { useState, useEffect } from "react"
import Image from "next/image"

const slides = [
  {
    image: "/img/57isaacjohn/2.jpg",
    title: "We understand people and place.",
    subtitle: "Creating architectural solutions that resonate with communities and environments",
  },
  {
    image: "/img/57isaacjohn/5.jpg",
    title: "We craft spaces that inspire.",
    subtitle: "Transforming visions into architectural masterpieces that elevate human experience",
  },
  {
    image: "/img/57isaacjohn/7.jpg",
    title: "We design for tomorrow.",
    subtitle: "Building sustainable futures through innovative architectural excellence",
  },
]

export default function HeroSection() {
  const [currentSlide, setCurrentSlide] = useState(0)
  const [isTransitioning, setIsTransitioning] = useState(false)

  useEffect(() => {
    const timer = setInterval(() => {
      if (!isTransitioning) {
        setIsTransitioning(true)
        setTimeout(() => {
          setCurrentSlide((prev) => (prev + 1) % slides.length)
          setIsTransitioning(false)
        }, 300)
      }
    }, 6000)
    return () => clearInterval(timer)
  }, [isTransitioning])

  return (
    <section className="relative h-screen overflow-hidden">
      {slides.map((slide, index) => (
        <div
          key={index}
          className={`absolute inset-0 transition-all duration-1000 ease-in-out ${
            index === currentSlide ? "opacity-100 scale-100" : "opacity-0 scale-105"
          }`}
        >
          <Image
            src={slide.image || "/placeholder.svg"}
            alt={slide.title}
            fill
            className="object-cover transition-transform duration-1000 ease-in-out"
            priority={index === 0}
            quality={100}
          />
          <div className="absolute inset-0 bg-gradient-to-r from-black/50 via-black/30 to-black/50" />
        </div>
      ))}

      <div className="absolute inset-0 flex items-center justify-center z-10">
        <div className="text-center text-white max-w-5xl px-4">
          <h1
            key={currentSlide}
            className="text-4xl md:text-6xl lg:text-7xl font-light mb-6 animate-fade-in-up leading-tight"
          >
            {slides[currentSlide].title}
          </h1>
          <p
            key={`subtitle-${currentSlide}`}
            className="text-lg md:text-xl lg:text-2xl text-gray-200 font-light max-w-4xl mx-auto leading-relaxed animate-fade-in-up animation-delay-300"
          >
            {slides[currentSlide].subtitle}
          </p>
        </div>
      </div>

      {/* Slide Indicators */}
      <div className="absolute bottom-8 left-1/2 transform -translate-x-1/2 flex space-x-3 z-20">
        {slides.map((_, index) => (
          <div
            key={index}
            className={`w-3 h-3 rounded-full transition-all duration-300 ${
              index === currentSlide ? "bg-white scale-125" : "bg-white/50"
            }`}
          />
        ))}
      </div>
    </section>
  )
}
