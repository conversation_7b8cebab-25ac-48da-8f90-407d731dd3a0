"use client"

import { useEffect, useRef } from "react"
import { MessageCircle, Search, Database, Users } from "lucide-react"

const processes = [
  {
    icon: MessageCircle,
    title: "Effective Communication",
    emoji: "💬",
  },
  {
    icon: Search,
    title: "Research & Strategy",
    emoji: "🔍",
  },
  {
    icon: Database,
    title: "Technology & Data Integration",
    emoji: "💻",
  },
  {
    icon: Users,
    title: "Extensive project collaboration",
    emoji: "🤝",
  },
]

export default function ProcessSection() {
  const sectionRef = useRef<HTMLElement>(null)

  useEffect(() => {
    const observer = new IntersectionObserver(
      (entries) => {
        entries.forEach((entry) => {
          if (entry.isIntersecting) {
            entry.target.classList.add("animate-fade-in-up")
          }
        })
      },
      { threshold: 0.1 },
    )

    const elements = sectionRef.current?.querySelectorAll(".animate-on-scroll")
    elements?.forEach((el) => observer.observe(el))

    return () => observer.disconnect()
  }, [])

  return (
    <section ref={sectionRef} className="py-20 bg-gray-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center mb-16 animate-on-scroll">
          <h2 className="text-4xl font-bold text-gray-900 mb-4 animate-on-scroll">Our Process</h2>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto animate-on-scroll animation-delay-100">
            We understand that embarking on a construction or design project can be daunting. That's why we offer a
            complete solution tailored to your needs.
          </p>
        </div>
        <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-8">
          {processes.map((process, index) => (
            <div key={process.title} className={`text-center animate-on-scroll animation-delay-${(index + 1) * 100}`}>
              <div className="text-6xl mb-6 animate-on-scroll animation-delay-${(index + 1) * 150}">
                {process.emoji}
              </div>
              <h3 className="text-xl font-semibold text-gray-900 mb-4 animate-on-scroll animation-delay-${(index + 1) * 200}">
                {process.title}
              </h3>
            </div>
          ))}
        </div>
      </div>
    </section>
  )
}
