"use client"

import { useEffect, useRef } from "react"
import { Building, Store, Home, Map } from "lucide-react"

const services = [
  {
    icon: Map,
    title: "Master Planning & Urban Design",
    description: "We specialize in shaping communities through thoughtful master planning and urban design strategies",
  },
  {
    icon: Store,
    title: "Retail Design",
    description: "Transforming retail environments into captivating and customer-centric spaces is our specialty.",
  },
  {
    icon: Building,
    title: "Commercial & Civic Design",
    description:
      "We excel in creating dynamic and functional commercial spaces tailored to our clients' business needs.",
  },
  {
    icon: Home,
    title: "Residential Design",
    description: "We design homes that resonate with the lifestyles and aspirations of our clients.",
  },
]

export default function ServicesSection() {
  const sectionRef = useRef<HTMLElement>(null)

  useEffect(() => {
    const observer = new IntersectionObserver(
      (entries) => {
        entries.forEach((entry) => {
          if (entry.isIntersecting) {
            entry.target.classList.add("animate-fade-in-up")
          }
        })
      },
      { threshold: 0.1 },
    )

    const elements = sectionRef.current?.querySelectorAll(".animate-on-scroll")
    elements?.forEach((el) => observer.observe(el))

    return () => observer.disconnect()
  }, [])

  return (
    <section ref={sectionRef} className="py-20 bg-white">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center mb-16 animate-on-scroll">
          <h2 className="text-4xl font-bold text-gray-900 mb-4 animate-on-scroll">Services - What We Do</h2>
        </div>
        <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-8">
          {services.map((service, index) => (
            <div key={service.title} className={`text-center animate-on-scroll animation-delay-${(index + 1) * 100}`}>
              <div className="bg-brand-light w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-6 animate-on-scroll animation-delay-${(index + 1) * 150}">
                <service.icon className="h-8 w-8 text-brand-primary" />
              </div>
              <h3 className="text-xl font-semibold text-gray-900 mb-4 animate-on-scroll animation-delay-${(index + 1) * 200}">
                {service.title}
              </h3>
              <p className="text-gray-600 leading-relaxed animate-on-scroll animation-delay-${(index + 1) * 250}">
                {service.description}
              </p>
            </div>
          ))}
        </div>
      </div>
    </section>
  )
}
