"use client"

import { useEffect, useRef } from "react"

export default function AboutSection() {
  const sectionRef = useRef<HTMLElement>(null)

  useEffect(() => {
    const observer = new IntersectionObserver(
      (entries) => {
        entries.forEach((entry) => {
          if (entry.isIntersecting) {
            entry.target.classList.add("animate-fade-in-up")
          }
        })
      },
      { threshold: 0.1 },
    )

    const elements = sectionRef.current?.querySelectorAll(".animate-on-scroll")
    elements?.forEach((el) => observer.observe(el))

    return () => observer.disconnect()
  }, [])

  return (
    <section ref={sectionRef} className="py-20 bg-gray-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="grid lg:grid-cols-2 gap-12 items-center">
          <div className="animate-on-scroll">
            <h2 className="text-4xl font-bold text-gray-900 mb-6 animate-on-scroll">About Us</h2>
            <p className="text-lg text-gray-600 mb-8 leading-relaxed animate-on-scroll animation-delay-100">
              CARVE Atelier is an architecture and design firm specializing in crafting innovative and practical
              solutions tailored to our clients' goals. Fueled by a deep-seated passion for design excellence, our
              approach is guided by principles that transform existing sites in unique ways, addressing both
              programmatic and operational needs.
            </p>
            <p className="text-lg text-gray-600 mb-8 leading-relaxed animate-on-scroll animation-delay-200">
              We provide professional services that not only meet but exceed our clients' expectations, delivering
              substantial value. Our team comprises informed, dynamic, and skilled professionals who collaborate closely
              across all project phases, united in their commitment to delivering high-quality service in design,
              documentation, and project execution.
            </p>
          </div>
          <div className="animate-on-scroll animation-delay-400">
            <video
              src="/img/photo/carve-art1.mp4"
              className="w-full h-80 object-cover hover:scale-105 transition-transform duration-300 rounded-lg shadow-lg animate-on-scroll"
              autoPlay
              muted
              loop
              playsInline
            />
          </div>
        </div>
      </div>
    </section>
  )
}
