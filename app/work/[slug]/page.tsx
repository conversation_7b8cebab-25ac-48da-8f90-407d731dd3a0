import type { Metadata } from "next"
import Navbar from "@/components/navbar"
import Footer from "@/components/footer"
import ProjectDetails from "@/components/project-details"
import PageBanner from "@/components/page-banner"
import projectsData from "@/data/projects.json"
import { findProjectBySlug } from "@/lib/utils"

type Props = {
  params: Promise<{ slug: string }>
}

export async function generateMetadata({ params }: Props): Promise<Metadata> {
  const { slug } = await params
  const project = findProjectBySlug(projectsData, slug)

  if (!project) {
    return {
      title: "Project Not Found - CARVE Atelier",
      description: "The requested project could not be found.",
    }
  }

  return {
    title: `${project.title} - CARVE Atelier Architecture Project`,
    description: `${project.description} Located in ${project.location}. ${project.fullDescription}`,
    keywords: [
      project.title,
      `${project.category} architecture`,
      `architecture ${project.location}`,
      "CARVE Atelier project",
      project.type,
      "Nigerian architecture",
      "building design",
      "architectural project",
    ],
    openGraph: {
      title: `${project.title} - CARVE Atelier`,
      description: project.description,
      url: `https://carveatelier.com/work/${slug}`,
      images: [
        {
          url: project.image || "/og-project.jpg",
          width: 1200,
          height: 630,
          alt: project.title,
        },
      ],
    },
    alternates: {
      canonical: `https://carveatelier.com/work/${slug}`,
    },
  }
}

export default async function ProjectPage({ params }: { params: Promise<{ slug: string }> }) {
  const { slug } = await params
  const project = findProjectBySlug(projectsData, slug)

  if (!project) {
    return (
      <main className="min-h-screen">
        <Navbar />
        <div className="flex items-center justify-center min-h-screen">
          <div className="text-center">
            <h1 className="text-4xl font-bold text-gray-900 mb-4">Project Not Found</h1>
            <p className="text-gray-600">The project you're looking for doesn't exist.</p>
          </div>
        </div>
        <Footer />
      </main>
    )
  }

  return (
    <main className="min-h-screen">
      <Navbar />
      <PageBanner
        title={project.title}
        subtitle="Discover the story behind our architectural creations"
        backgroundImage={project.image}
        breadcrumbs={[{ label: "Home", href: "/" }, { label: "Our Work", href: "/work" }, { label: project.title }]}
      />
      <ProjectDetails projectSlug={slug} />
      <Footer />
    </main>
  )
}
