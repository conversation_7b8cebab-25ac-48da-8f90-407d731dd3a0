import type { Metadata } from "next"
import Navbar from "@/components/navbar"
import Footer from "@/components/footer"
import WhatWeDoSection from "@/components/what-we-do-section"
import UnveilingSection from "@/components/unveiling-section"
import CompanyLogosSection from "@/components/company-logos-section"
import TestimonialsSection from "@/components/testimonials-section"
import CareerSection from "@/components/career-section"
import PageBanner from "@/components/page-banner"

export const metadata: Metadata = {
  title: "About Us - CARVE Atelier | Leading Architecture Firm in Lagos",
  description:
    "Learn about CARVE Atelier, a premier architecture and design firm in Lagos, Nigeria. Discover our passion for innovative design, our expert team, and our commitment to transforming spaces and communities.",
  keywords: [
    "about CARVE Atelier",
    "architecture firm history",
    "Nigerian architecture company",
    "design philosophy",
    "architectural team Lagos",
    "building design expertise",
    "sustainable architecture Nigeria",
    "innovative design solutions",
    "architectural services Lagos",
    "design excellence Nigeria",
  ],
  openGraph: {
    title: "About CARVE Atelier - Leading Architecture Firm",
    description:
      "Discover CARVE Atelier's passion for innovative architectural design and our commitment to transforming spaces in Lagos, Nigeria.",
    url: "https://carveatelier.com/about",
    images: [
      {
        url: "/og-about.jpg",
        width: 1200,
        height: 630,
        alt: "About CARVE Atelier",
      },
    ],
  },
  alternates: {
    canonical: "https://carveatelier.com/about",
  },
}

export default function About() {
  return (
    <main className="min-h-screen">
      <Navbar />
      <PageBanner
        title="About Us"
        subtitle="Crafting innovative architectural solutions with passion and precision"
        backgroundImage="/img/photo/office8.webp"
        breadcrumbs={[{ label: "Home", href: "/" }, { label: "About Us" }]}
      />
      <WhatWeDoSection />
      <UnveilingSection />
      <CompanyLogosSection />
      <TestimonialsSection />
      <CareerSection />
      <Footer />
    </main>
  )
}
