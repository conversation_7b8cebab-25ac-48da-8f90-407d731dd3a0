@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 222.2 84% 4.9%;
    --card: 0 0% 100%;
    --card-foreground: 222.2 84% 4.9%;
    --popover: 0 0% 100%;
    --popover-foreground: 222.2 84% 4.9%;
    --primary: 222.2 47.4% 11.2%;
    --primary-foreground: 210 40% 98%;
    --secondary: 210 40% 96%;
    --secondary-foreground: 222.2 84% 4.9%;
    --muted: 210 40% 96%;
    --muted-foreground: 215.4 16.3% 46.9%;
    --accent: 210 40% 96%;
    --accent-foreground: 222.2 84% 4.9%;
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;
    --border: 214.3 31.8% 91.4%;
    --input: 214.3 31.8% 91.4%;
    --ring: 222.2 84% 4.9%;
    --radius: 0.5rem;
    --chart-1: 12 76% 61%;
    --chart-2: 173 58% 39%;
    --chart-3: 197 37% 24%;
    --chart-4: 43 74% 66%;
    --chart-5: 27 87% 67%;

    /* Custom brand colors */
    --brand-primary: 18 65% 57%; /* #d77047 */
    --brand-secondary: 18 75% 67%; /* #e08a5f */
    --brand-accent: 18 55% 47%; /* #c55a2f */
    --brand-light: 18 85% 87%; /* #f5e6d8 */
    --brand-dark: 18 45% 37%; /* #a3441f */
  }

  .dark {
    --background: 222.2 84% 4.9%;
    --foreground: 210 40% 98%;
    --card: 222.2 84% 4.9%;
    --card-foreground: 210 40% 98%;
    --popover: 222.2 84% 4.9%;
    --popover-foreground: 210 40% 98%;
    --primary: 210 40% 98%;
    --primary-foreground: 222.2 47.4% 11.2%;
    --secondary: 217.2 32.6% 17.5%;
    --secondary-foreground: 210 40% 98%;
    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;
    --accent: 217.2 32.6% 17.5%;
    --accent-foreground: 210 40% 98%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;
    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
    --ring: 212.7 26.8% 83.9%;
    --chart-1: 220 70% 50%;
    --chart-2: 160 60% 45%;
    --chart-3: 30 80% 55%;
    --chart-4: 280 65% 60%;
    --chart-5: 340 75% 55%;
  }
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
  }
}

@layer utilities {
  .bg-brand-primary {
    background-color: hsl(var(--brand-primary));
  }
  .bg-brand-secondary {
    background-color: hsl(var(--brand-secondary));
  }
  .bg-brand-accent {
    background-color: hsl(var(--brand-accent));
  }
  .bg-brand-light {
    background-color: hsl(var(--brand-light));
  }
  .bg-brand-dark {
    background-color: hsl(var(--brand-dark));
  }

  .text-brand-primary {
    color: hsl(var(--brand-primary));
  }
  .text-brand-secondary {
    color: hsl(var(--brand-secondary));
  }
  .text-brand-accent {
    color: hsl(var(--brand-accent));
  }
  .text-brand-light {
    color: hsl(var(--brand-light));
  }
  .text-brand-dark {
    color: hsl(var(--brand-dark));
  }

  .border-brand-primary {
    border-color: hsl(var(--brand-primary));
  }
  .border-brand-secondary {
    border-color: hsl(var(--brand-secondary));
  }
  .border-brand-accent {
    border-color: hsl(var(--brand-accent));
  }

  .hover\:bg-brand-primary:hover {
    background-color: hsl(var(--brand-primary));
  }
  .hover\:bg-brand-secondary:hover {
    background-color: hsl(var(--brand-secondary));
  }
  .hover\:bg-brand-accent:hover {
    background-color: hsl(var(--brand-accent));
  }
  .hover\:bg-brand-dark:hover {
    background-color: hsl(var(--brand-dark));
  }

  .hover\:text-brand-primary:hover {
    color: hsl(var(--brand-primary));
  }
  .hover\:text-brand-secondary:hover {
    color: hsl(var(--brand-secondary));
  }
  .hover\:text-brand-accent:hover {
    color: hsl(var(--brand-accent));
  }

  .bg-gradient-brand {
    background: linear-gradient(135deg, hsl(var(--brand-primary)), hsl(var(--brand-secondary)));
  }
  .bg-gradient-brand-reverse {
    background: linear-gradient(135deg, hsl(var(--brand-secondary)), hsl(var(--brand-primary)));
  }
  .bg-gradient-brand-dark {
    background: linear-gradient(135deg, hsl(var(--brand-accent)), hsl(var(--brand-dark)));
  }
}

/* Custom animations */
@keyframes fade-in-up {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fade-in-out {
  0%,
  100% {
    opacity: 0;
    transform: translateY(10px);
  }
  50% {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes scroll {
  0% {
    transform: translateX(0);
  }
  100% {
    transform: translateX(-50%);
  }
}

@keyframes float-1 {
  0%,
  100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-10px);
  }
}

@keyframes float-2 {
  0%,
  100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-15px);
  }
}

@keyframes float-3 {
  0%,
  100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-8px);
  }
}

@keyframes slide-in-left {
  from {
    opacity: 0;
    transform: translateX(-50px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes slide-in-right {
  from {
    opacity: 0;
    transform: translateX(50px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes scale-in {
  from {
    opacity: 0;
    transform: scale(0.8);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

@keyframes hero-scale {
  from {
    transform: scale(1.05);
    opacity: 0;
  }
  to {
    transform: scale(1);
    opacity: 1;
  }
}

@keyframes float {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-20px);
  }
}

@keyframes floatDelayed {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-15px);
  }
}

@keyframes shimmer {
  0% {
    transform: translateX(-100%);
  }
  100% {
    transform: translateX(100%);
  }
}

@keyframes growHeight {
  from {
    height: 0;
  }
  to {
    height: 100%;
  }
}

.animate-fade-in-up {
  animation: fade-in-up 0.8s ease-out forwards;
}

.animate-fade-in-out {
  animation: fade-in-out 3s ease-in-out infinite;
}

.animate-scroll {
  animation: scroll 30s linear infinite;
}

.animate-float-1 {
  animation: float-1 6s ease-in-out infinite;
}

.animate-float-2 {
  animation: float-2 8s ease-in-out infinite;
}

.animate-float-3 {
  animation: float-3 7s ease-in-out infinite;
}

.animate-slide-in-left {
  animation: slide-in-left 0.8s ease-out forwards;
}

.animate-slide-in-right {
  animation: slide-in-right 0.8s ease-out forwards;
}

.animate-scale-in {
  animation: scale-in 0.8s ease-out forwards;
}

.animate-hero-scale {
  animation: hero-scale 1s ease-out forwards;
}

.animate-float {
  animation: float 3s ease-in-out infinite;
}

.animate-float-delayed {
  animation: floatDelayed 4s ease-in-out infinite;
}

.animate-shimmer {
  animation: shimmer 2s ease-in-out infinite;
}

.animate-grow-height {
  animation: growHeight 1.5s ease-out forwards;
}

/* Animation delays */
.animation-delay-50 {
  animation-delay: 50ms;
}

.animation-delay-100 {
  animation-delay: 100ms;
}

.animation-delay-150 {
  animation-delay: 150ms;
}

.animation-delay-200 {
  animation-delay: 200ms;
}

.animation-delay-250 {
  animation-delay: 250ms;
}

.animation-delay-300 {
  animation-delay: 300ms;
}

.animation-delay-350 {
  animation-delay: 350ms;
}

.animation-delay-400 {
  animation-delay: 400ms;
}

.animation-delay-450 {
  animation-delay: 450ms;
}

.animation-delay-500 {
  animation-delay: 500ms;
}

.animation-delay-550 {
  animation-delay: 550ms;
}

.animation-delay-600 {
  animation-delay: 600ms;
}

.animation-delay-700 {
  animation-delay: 700ms;
}

.animation-delay-800 {
  animation-delay: 800ms;
}

.animation-delay-1000 {
  animation-delay: 1000ms;
}

.animation-delay-1200 {
  animation-delay: 1200ms;
}

/* Initial state for animations */
.animate-on-scroll {
  opacity: 0;
  transform: translateY(30px);
}

/* Line clamp utility */
.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.line-clamp-3 {
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

/* Smooth scrolling */
html {
  scroll-behavior: smooth;
}

/* Scroll padding for fixed navbar */
html {
  scroll-padding-top: 5rem; /* 80px for navbar height */
}

/* Hero section specific styles */
.hero-text-animate {
  animation: fade-in-up 1s ease-out forwards;
}
